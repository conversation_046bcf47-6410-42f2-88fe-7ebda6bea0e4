{"backtrace": 0, "backtraceGraph": {"commands": [], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}]}, "dependencies": [{"id": "Client_autogen_timestamp_deps::@6890427a1f51a3e7e1df"}], "id": "Client_autogen::@6890427a1f51a3e7e1df", "isGeneratorProvided": true, "name": "Client_autogen", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1, 2]}], "sources": [{"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/CMakeFiles/Client_autogen", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/CMakeFiles/Client_autogen.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/Client_autogen/timestamp.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}