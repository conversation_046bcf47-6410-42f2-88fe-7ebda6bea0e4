{"artifacts": [{"path": "Client.exe"}, {"path": "Client.pdb"}], "backtrace": 4, "backtraceGraph": {"commands": ["add_executable", "_qt_internal_create_executable", "qt6_add_executable", "qt_add_executable", "install", "target_link_libraries", "set_target_properties", "include", "find_package", "find_dependency", "_qt_internal_find_qt_dependencies"], "files": ["E:/YingYong/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreMacros.cmake", "CMakeLists.txt", "E:/YingYong/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsTargets.cmake", "E:/YingYong/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsConfig.cmake", "build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc/package-manager/maintenance_tool_provider.cmake", "E:/YingYong/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreTargets.cmake", "E:/YingYong/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake", "E:/YingYong/QT/Tools/CMake_64/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake", "E:/YingYong/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake", "E:/YingYong/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsDependencies.cmake", "E:/YingYong/QT/6.9.1/msvc2022_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateTargets.cmake", "E:/YingYong/QT/6.9.1/msvc2022_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateConfig.cmake", "E:/YingYong/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreDependencies.cmake", "E:/YingYong/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6GuiTargets.cmake", "E:/YingYong/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6GuiConfig.cmake"], "nodes": [{"file": 1}, {"command": 3, "file": 1, "line": 23, "parent": 0}, {"command": 2, "file": 0, "line": 935, "parent": 1}, {"command": 1, "file": 0, "line": 639, "parent": 2}, {"command": 0, "file": 0, "line": 690, "parent": 3}, {"command": 4, "file": 1, "line": 62, "parent": 0}, {"command": 5, "file": 1, "line": 45, "parent": 0}, {"command": 8, "file": 1, "line": 13, "parent": 0}, {"command": 8, "file": 4, "line": 297, "parent": 7}, {"file": 3, "parent": 8}, {"command": 7, "file": 3, "line": 55, "parent": 9}, {"file": 2, "parent": 10}, {"command": 6, "file": 2, "line": 61, "parent": 11}, {"command": 5, "file": 0, "line": 640, "parent": 2}, {"command": 7, "file": 3, "line": 43, "parent": 9}, {"file": 9, "parent": 14}, {"command": 10, "file": 9, "line": 45, "parent": 15}, {"command": 9, "file": 8, "line": 137, "parent": 16}, {"command": 8, "file": 7, "line": 76, "parent": 17}, {"command": 8, "file": 4, "line": 315, "parent": 18}, {"file": 6, "parent": 19}, {"command": 7, "file": 6, "line": 57, "parent": 20}, {"file": 5, "parent": 21}, {"command": 6, "file": 5, "line": 61, "parent": 22}, {"command": 7, "file": 6, "line": 45, "parent": 20}, {"file": 12, "parent": 24}, {"command": 10, "file": 12, "line": 46, "parent": 25}, {"command": 9, "file": 8, "line": 137, "parent": 26}, {"command": 8, "file": 7, "line": 76, "parent": 27}, {"command": 8, "file": 4, "line": 315, "parent": 28}, {"file": 11, "parent": 29}, {"command": 7, "file": 11, "line": 55, "parent": 30}, {"file": 10, "parent": 31}, {"command": 6, "file": 10, "line": 61, "parent": 32}, {"command": 6, "file": 10, "line": 83, "parent": 32}, {"command": 9, "file": 8, "line": 137, "parent": 16}, {"command": 8, "file": 7, "line": 76, "parent": 35}, {"command": 8, "file": 4, "line": 315, "parent": 36}, {"file": 14, "parent": 37}, {"command": 7, "file": 14, "line": 55, "parent": 38}, {"file": 13, "parent": 39}, {"command": 6, "file": 13, "line": 61, "parent": 40}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-DQT_QML_DEBUG /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd"}, {"backtrace": 13, "fragment": "-Zc:__cplusplus"}, {"backtrace": 13, "fragment": "-permissive-"}, {"backtrace": 13, "fragment": "-utf-8"}], "defines": [{"backtrace": 13, "define": "QT_CORE_LIB"}, {"backtrace": 6, "define": "QT_GUI_LIB"}, {"backtrace": 6, "define": "QT_WIDGETS_LIB"}, {"backtrace": 13, "define": "UNICODE"}, {"backtrace": 13, "define": "WIN32"}, {"backtrace": 13, "define": "WIN64"}, {"backtrace": 13, "define": "_ENABLE_EXTENDED_ALIGNED_STORAGE"}, {"backtrace": 13, "define": "_UNICODE"}, {"backtrace": 13, "define": "_WIN64"}], "includes": [{"backtrace": 0, "path": "D:/Desktop_xiao/100/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/Client_autogen/include"}, {"backtrace": 13, "isSystem": true, "path": "E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore"}, {"backtrace": 13, "isSystem": true, "path": "E:/YingYong/QT/6.9.1/msvc2022_64/include"}, {"backtrace": 13, "isSystem": true, "path": "E:/YingYong/QT/6.9.1/msvc2022_64/mkspecs/win32-msvc"}, {"backtrace": 6, "isSystem": true, "path": "E:/YingYong/QT/6.9.1/msvc2022_64/include/QtWidgets"}, {"backtrace": 6, "isSystem": true, "path": "E:/YingYong/QT/6.9.1/msvc2022_64/include/QtGui"}], "language": "CXX", "languageStandard": {"backtraces": [13, 13], "standard": "17"}, "sourceIndexes": [0, 1, 2]}], "dependencies": [{"backtrace": 0, "id": "Client_autogen::@6890427a1f51a3e7e1df"}, {"id": "Client_autogen_timestamp_deps::@6890427a1f51a3e7e1df"}], "id": "Client::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 5, "path": "bin"}], "prefix": {"path": "C:/Program Files (x86)/Client"}}, "link": {"commandFragments": [{"fragment": "-DQT_QML_DEBUG /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -MDd", "role": "flags"}, {"fragment": "/machine:x64 /debug /INCREMENTAL /subsystem:windows", "role": "flags"}, {"backtrace": 6, "fragment": "E:\\YingYong\\QT\\6.9.1\\msvc2022_64\\lib\\Qt6Widgetsd.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "E:\\YingYong\\QT\\6.9.1\\msvc2022_64\\lib\\Qt6Guid.lib", "role": "libraries"}, {"backtrace": 13, "fragment": "E:\\YingYong\\QT\\6.9.1\\msvc2022_64\\lib\\Qt6Cored.lib", "role": "libraries"}, {"backtrace": 23, "fragment": "mpr.lib", "role": "libraries"}, {"backtrace": 23, "fragment": "userenv.lib", "role": "libraries"}, {"backtrace": 33, "fragment": "E:\\YingYong\\QT\\6.9.1\\msvc2022_64\\lib\\Qt6EntryPointd.lib", "role": "libraries"}, {"backtrace": 34, "fragment": "shell32.lib", "role": "libraries"}, {"backtrace": 41, "fragment": "d3d11.lib", "role": "libraries"}, {"backtrace": 41, "fragment": "dxgi.lib", "role": "libraries"}, {"backtrace": 41, "fragment": "dxguid.lib", "role": "libraries"}, {"backtrace": 41, "fragment": "d3d12.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "Client", "nameOnDisk": "Client.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [3, 5]}, {"name": "Forms", "sourceIndexes": [4]}, {"name": "", "sourceIndexes": [6]}, {"name": "CMake Rules", "sourceIndexes": [7]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/Client_autogen/mocs_compilation.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "compileGroupIndex": 0, "path": "main.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "compileGroupIndex": 0, "path": "mainwindow.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "path": "mainwindow.h", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "mainwindow.ui", "sourceGroupIndex": 2}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/Client_autogen/include/ui_mainwindow.h", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/Client_autogen/timestamp", "sourceGroupIndex": 3}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/Client_autogen/timestamp.rule", "sourceGroupIndex": 4}], "type": "EXECUTABLE"}