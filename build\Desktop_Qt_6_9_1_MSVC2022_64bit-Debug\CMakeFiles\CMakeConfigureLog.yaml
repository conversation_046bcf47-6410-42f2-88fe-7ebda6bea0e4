
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "E:/YingYong/QT/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:3 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "E:/YingYong/QT/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "E:/YingYong/QT/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "E:/YingYong/QT/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: E:/YingYong/VisualStudio/Community/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/cl.exe 
      Build flags: -DQT_QML_DEBUG
      Id flags:  
      
      The output was:
      0
      用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.44.35207.1 版
      版权所有(C) Microsoft Corporation。保留所有权利。
      
      CMakeCXXCompilerId.cpp
      Microsoft (R) Incremental Linker Version 14.44.35207.1
      Copyright (C) Microsoft Corporation.  All rights reserved.
      
      /out:CMakeCXXCompilerId.exe 
      CMakeCXXCompilerId.obj 
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.obj"
      
      The CXX compiler identification is MSVC, found in:
        D:/Desktop_xiao/100/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/CMakeFiles/3.30.5/CompilerIdCXX/CMakeCXXCompilerId.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "E:/YingYong/QT/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:1243 (message)"
      - "E:/YingYong/QT/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:250 (CMAKE_DETERMINE_MSVC_SHOWINCLUDES_PREFIX)"
      - "E:/YingYong/QT/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Detecting CXX compiler /showIncludes prefix:
        main.c
        注意: 包含文件:  D:\\Desktop_xiao\\100\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\CMakeFiles\\ShowIncludes\\foo.h
        
      Found prefix "注意: 包含文件:  "
  -
    kind: "try_compile-v1"
    backtrace:
      - "E:/YingYong/QT/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "E:/YingYong/QT/Tools/CMake_64/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "D:/Desktop_xiao/100/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-cgz563"
      binary: "D:/Desktop_xiao/100/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-cgz563"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "-DQT_QML_DEBUG /DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/Desktop_xiao/100/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-cgz563'
        
        Run Build Command(s): E:/YingYong/VisualStudio/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe -v cmTC_2b781
        [1/2] E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\cl.exe  /nologo /TP   -DQT_QML_DEBUG /DWIN32 /D_WINDOWS /GR /EHsc  /Zi /Ob0 /Od /RTC1 -MDd /showIncludes /FoCMakeFiles\\cmTC_2b781.dir\\CMakeCXXCompilerABI.cpp.obj /FdCMakeFiles\\cmTC_2b781.dir\\ /FS -c E:\\YingYong\\QT\\Tools\\CMake_64\\share\\cmake-3.30\\Modules\\CMakeCXXCompilerABI.cpp
        [2/2] C:\\WINDOWS\\system32\\cmd.exe /C "cd . && E:\\YingYong\\QT\\Tools\\CMake_64\\bin\\cmake.exe -E vs_link_exe --intdir=CMakeFiles\\cmTC_2b781.dir --rc=D:\\WINDOW~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=D:\\WINDOW~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_2b781.dir\\CMakeCXXCompilerABI.cpp.obj  /out:cmTC_2b781.exe /implib:cmTC_2b781.lib /pdb:cmTC_2b781.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "E:/YingYong/QT/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "E:/YingYong/QT/Tools/CMake_64/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': E:/YingYong/VisualStudio/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "E:/YingYong/QT/Tools/CMake_64/share/cmake-3.30/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "E:/YingYong/QT/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "E:/YingYong/QT/Tools/CMake_64/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Running the CXX compiler's linker: "E:/YingYong/VisualStudio/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.44.35207.1
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "E:/YingYong/QT/Tools/CMake_64/share/cmake-3.30/Modules/Internal/CheckSourceCompiles.cmake:101 (try_compile)"
      - "E:/YingYong/QT/Tools/CMake_64/share/cmake-3.30/Modules/CheckCXXSourceCompiles.cmake:52 (cmake_check_source_compiles)"
      - "E:/YingYong/QT/Tools/CMake_64/share/cmake-3.30/Modules/FindThreads.cmake:99 (CHECK_CXX_SOURCE_COMPILES)"
      - "E:/YingYong/QT/Tools/CMake_64/share/cmake-3.30/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc/package-manager/maintenance_tool_provider.cmake:322 (find_package)"
      - "E:/YingYong/QT/Tools/CMake_64/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "E:/YingYong/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "E:/YingYong/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/Qt6Dependencies.cmake:35 (_qt_internal_find_third_party_dependencies)"
      - "E:/YingYong/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/Qt6Config.cmake:168 (include)"
      - "build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc/package-manager/maintenance_tool_provider.cmake:322 (find_package)"
      - "CMakeLists.txt:12 (find_package)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "D:/Desktop_xiao/100/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-q0yuw1"
      binary: "D:/Desktop_xiao/100/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-q0yuw1"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "-DQT_QML_DEBUG /DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "E:/YingYong/QT/6.9.1/msvc2022_64/lib/cmake/Qt6;E:/YingYong/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;E:/YingYong/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: 'D:/Desktop_xiao/100/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-q0yuw1'
        
        Run Build Command(s): E:/YingYong/VisualStudio/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe -v cmTC_2cd36
        [1/2] E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\cl.exe  /nologo /TP -DCMAKE_HAVE_LIBC_PTHREAD  -DQT_QML_DEBUG /DWIN32 /D_WINDOWS /GR /EHsc  /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /showIncludes /FoCMakeFiles\\cmTC_2cd36.dir\\src.cxx.obj /FdCMakeFiles\\cmTC_2cd36.dir\\ /FS -c D:\\Desktop_xiao\\100\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\CMakeFiles\\CMakeScratch\\TryCompile-q0yuw1\\src.cxx
        \x1b[31mFAILED: \x1b[0mCMakeFiles/cmTC_2cd36.dir/src.cxx.obj 
        E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\cl.exe  /nologo /TP -DCMAKE_HAVE_LIBC_PTHREAD  -DQT_QML_DEBUG /DWIN32 /D_WINDOWS /GR /EHsc  /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /showIncludes /FoCMakeFiles\\cmTC_2cd36.dir\\src.cxx.obj /FdCMakeFiles\\cmTC_2cd36.dir\\ /FS -c D:\\Desktop_xiao\\100\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\CMakeFiles\\CMakeScratch\\TryCompile-q0yuw1\\src.cxx
        D:\\Desktop_xiao\\100\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\CMakeFiles\\CMakeScratch\\TryCompile-q0yuw1\\src.cxx(1): fatal error C1083: 无法打开包括文件: “pthread.h”: No such file or directory
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "E:/YingYong/QT/Tools/CMake_64/share/cmake-3.30/Modules/CheckLibraryExists.cmake:69 (try_compile)"
      - "E:/YingYong/QT/Tools/CMake_64/share/cmake-3.30/Modules/FindThreads.cmake:112 (CHECK_LIBRARY_EXISTS)"
      - "E:/YingYong/QT/Tools/CMake_64/share/cmake-3.30/Modules/FindThreads.cmake:175 (_threads_check_lib)"
      - "build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc/package-manager/maintenance_tool_provider.cmake:322 (find_package)"
      - "E:/YingYong/QT/Tools/CMake_64/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "E:/YingYong/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "E:/YingYong/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/Qt6Dependencies.cmake:35 (_qt_internal_find_third_party_dependencies)"
      - "E:/YingYong/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/Qt6Config.cmake:168 (include)"
      - "build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc/package-manager/maintenance_tool_provider.cmake:322 (find_package)"
      - "CMakeLists.txt:12 (find_package)"
    checks:
      - "Looking for pthread_create in pthreads"
    directories:
      source: "D:/Desktop_xiao/100/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-gou63f"
      binary: "D:/Desktop_xiao/100/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-gou63f"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "-DQT_QML_DEBUG /DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "E:/YingYong/QT/6.9.1/msvc2022_64/lib/cmake/Qt6;E:/YingYong/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;E:/YingYong/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "CMAKE_HAVE_PTHREADS_CREATE"
      cached: true
      stdout: |
        Change Dir: 'D:/Desktop_xiao/100/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-gou63f'
        
        Run Build Command(s): E:/YingYong/VisualStudio/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe -v cmTC_0225f
        [1/2] E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\cl.exe  /nologo /TP   -DQT_QML_DEBUG /DWIN32 /D_WINDOWS /GR /EHsc -DCHECK_FUNCTION_EXISTS=pthread_create /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /showIncludes /FoCMakeFiles\\cmTC_0225f.dir\\CheckFunctionExists.cxx.obj /FdCMakeFiles\\cmTC_0225f.dir\\ /FS -c D:\\Desktop_xiao\\100\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\CMakeFiles\\CMakeScratch\\TryCompile-gou63f\\CheckFunctionExists.cxx
        [2/2] C:\\WINDOWS\\system32\\cmd.exe /C "cd . && E:\\YingYong\\QT\\Tools\\CMake_64\\bin\\cmake.exe -E vs_link_exe --intdir=CMakeFiles\\cmTC_0225f.dir --rc=D:\\WINDOW~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=D:\\WINDOW~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_0225f.dir\\CheckFunctionExists.cxx.obj  /out:cmTC_0225f.exe /implib:cmTC_0225f.lib /pdb:cmTC_0225f.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  pthreads.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        \x1b[31mFAILED: \x1b[0mcmTC_0225f.exe 
        C:\\WINDOWS\\system32\\cmd.exe /C "cd . && E:\\YingYong\\QT\\Tools\\CMake_64\\bin\\cmake.exe -E vs_link_exe --intdir=CMakeFiles\\cmTC_0225f.dir --rc=D:\\WINDOW~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=D:\\WINDOW~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_0225f.dir\\CheckFunctionExists.cxx.obj  /out:cmTC_0225f.exe /implib:cmTC_0225f.lib /pdb:cmTC_0225f.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  pthreads.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        LINK Pass 1: command "E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_0225f.dir\\CheckFunctionExists.cxx.obj /out:cmTC_0225f.exe /implib:cmTC_0225f.lib /pdb:cmTC_0225f.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console pthreads.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTFILE:CMakeFiles\\cmTC_0225f.dir/intermediate.manifest CMakeFiles\\cmTC_0225f.dir/manifest.res" failed (exit code 1104) with the following output:
        LINK : fatal error LNK1104: 无法打开文件“pthreads.lib”\x0d
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "E:/YingYong/QT/Tools/CMake_64/share/cmake-3.30/Modules/CheckLibraryExists.cmake:69 (try_compile)"
      - "E:/YingYong/QT/Tools/CMake_64/share/cmake-3.30/Modules/FindThreads.cmake:112 (CHECK_LIBRARY_EXISTS)"
      - "E:/YingYong/QT/Tools/CMake_64/share/cmake-3.30/Modules/FindThreads.cmake:176 (_threads_check_lib)"
      - "build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc/package-manager/maintenance_tool_provider.cmake:322 (find_package)"
      - "E:/YingYong/QT/Tools/CMake_64/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "E:/YingYong/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "E:/YingYong/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/Qt6Dependencies.cmake:35 (_qt_internal_find_third_party_dependencies)"
      - "E:/YingYong/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/Qt6Config.cmake:168 (include)"
      - "build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc/package-manager/maintenance_tool_provider.cmake:322 (find_package)"
      - "CMakeLists.txt:12 (find_package)"
    checks:
      - "Looking for pthread_create in pthread"
    directories:
      source: "D:/Desktop_xiao/100/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-sg3opk"
      binary: "D:/Desktop_xiao/100/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-sg3opk"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "-DQT_QML_DEBUG /DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "E:/YingYong/QT/6.9.1/msvc2022_64/lib/cmake/Qt6;E:/YingYong/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;E:/YingYong/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "CMAKE_HAVE_PTHREAD_CREATE"
      cached: true
      stdout: |
        Change Dir: 'D:/Desktop_xiao/100/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-sg3opk'
        
        Run Build Command(s): E:/YingYong/VisualStudio/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe -v cmTC_c5b87
        [1/2] E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\cl.exe  /nologo /TP   -DQT_QML_DEBUG /DWIN32 /D_WINDOWS /GR /EHsc -DCHECK_FUNCTION_EXISTS=pthread_create /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /showIncludes /FoCMakeFiles\\cmTC_c5b87.dir\\CheckFunctionExists.cxx.obj /FdCMakeFiles\\cmTC_c5b87.dir\\ /FS -c D:\\Desktop_xiao\\100\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\CMakeFiles\\CMakeScratch\\TryCompile-sg3opk\\CheckFunctionExists.cxx
        [2/2] C:\\WINDOWS\\system32\\cmd.exe /C "cd . && E:\\YingYong\\QT\\Tools\\CMake_64\\bin\\cmake.exe -E vs_link_exe --intdir=CMakeFiles\\cmTC_c5b87.dir --rc=D:\\WINDOW~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=D:\\WINDOW~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_c5b87.dir\\CheckFunctionExists.cxx.obj  /out:cmTC_c5b87.exe /implib:cmTC_c5b87.lib /pdb:cmTC_c5b87.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  pthread.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        \x1b[31mFAILED: \x1b[0mcmTC_c5b87.exe 
        C:\\WINDOWS\\system32\\cmd.exe /C "cd . && E:\\YingYong\\QT\\Tools\\CMake_64\\bin\\cmake.exe -E vs_link_exe --intdir=CMakeFiles\\cmTC_c5b87.dir --rc=D:\\WINDOW~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=D:\\WINDOW~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_c5b87.dir\\CheckFunctionExists.cxx.obj  /out:cmTC_c5b87.exe /implib:cmTC_c5b87.lib /pdb:cmTC_c5b87.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  pthread.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        LINK Pass 1: command "E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_c5b87.dir\\CheckFunctionExists.cxx.obj /out:cmTC_c5b87.exe /implib:cmTC_c5b87.lib /pdb:cmTC_c5b87.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console pthread.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTFILE:CMakeFiles\\cmTC_c5b87.dir/intermediate.manifest CMakeFiles\\cmTC_c5b87.dir/manifest.res" failed (exit code 1104) with the following output:
        LINK : fatal error LNK1104: 无法打开文件“pthread.lib”\x0d
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "E:/YingYong/QT/Tools/CMake_64/share/cmake-3.30/Modules/Internal/CheckSourceCompiles.cmake:101 (try_compile)"
      - "E:/YingYong/QT/Tools/CMake_64/share/cmake-3.30/Modules/CheckCXXSourceCompiles.cmake:52 (cmake_check_source_compiles)"
      - "E:/YingYong/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/FindWrapAtomic.cmake:36 (check_cxx_source_compiles)"
      - "build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc/package-manager/maintenance_tool_provider.cmake:322 (find_package)"
      - "E:/YingYong/QT/Tools/CMake_64/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "E:/YingYong/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "E:/YingYong/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreDependencies.cmake:35 (_qt_internal_find_third_party_dependencies)"
      - "E:/YingYong/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake:45 (include)"
      - "build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc/package-manager/maintenance_tool_provider.cmake:315 (find_package)"
      - "E:/YingYong/QT/Tools/CMake_64/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "E:/YingYong/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:137 (find_dependency)"
      - "E:/YingYong/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsDependencies.cmake:45 (_qt_internal_find_qt_dependencies)"
      - "E:/YingYong/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsConfig.cmake:43 (include)"
      - "build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc/package-manager/maintenance_tool_provider.cmake:297 (find_package)"
      - "CMakeLists.txt:13 (find_package)"
    checks:
      - "Performing Test HAVE_STDATOMIC"
    directories:
      source: "D:/Desktop_xiao/100/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-ey8m8z"
      binary: "D:/Desktop_xiao/100/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-ey8m8z"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "-DQT_QML_DEBUG /DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "E:/YingYong/QT/6.9.1/msvc2022_64/lib/cmake/Qt6;E:/YingYong/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;E:/YingYong/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "HAVE_STDATOMIC"
      cached: true
      stdout: |
        Change Dir: 'D:/Desktop_xiao/100/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-ey8m8z'
        
        Run Build Command(s): E:/YingYong/VisualStudio/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe -v cmTC_6ae1a
        [1/2] E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\cl.exe  /nologo /TP -DHAVE_STDATOMIC  -DQT_QML_DEBUG /DWIN32 /D_WINDOWS /GR /EHsc  /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /showIncludes /FoCMakeFiles\\cmTC_6ae1a.dir\\src.cxx.obj /FdCMakeFiles\\cmTC_6ae1a.dir\\ /FS -c D:\\Desktop_xiao\\100\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\CMakeFiles\\CMakeScratch\\TryCompile-ey8m8z\\src.cxx
        [2/2] C:\\WINDOWS\\system32\\cmd.exe /C "cd . && E:\\YingYong\\QT\\Tools\\CMake_64\\bin\\cmake.exe -E vs_link_exe --intdir=CMakeFiles\\cmTC_6ae1a.dir --rc=D:\\WINDOW~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=D:\\WINDOW~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_6ae1a.dir\\src.cxx.obj  /out:cmTC_6ae1a.exe /implib:cmTC_6ae1a.lib /pdb:cmTC_6ae1a.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...
