# This file is managed by Qt Creator, do not edit!

set("QT_QMAKE_EXECUTABLE" "E:/YingYong/QT/6.9.1/msvc2022_64/bin/qmake.exe" CACHE "FILEPATH" "" FORCE)
set("CMAKE_PREFIX_PATH" "E:/YingYong/QT/6.9.1/msvc2022_64" CACHE "PATH" "" FORCE)
set("CMAKE_CXX_FLAGS_INIT" "-DQT_QML_DEBUG" CACHE "STRING" "" FORCE)
set("CMAKE_C_COMPILER" "E:/YingYong/VisualStudio/Community/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/cl.exe" CACHE "FILEPATH" "" FORCE)
set("CMAKE_PROJECT_INCLUDE_BEFORE" "D:/Desktop_xiao/100/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc/package-manager/auto-setup.cmake" CACHE "FILEPATH" "" FORCE)
set("CMAKE_GENERATOR" "Ninja" CACHE "STRING" "" FORCE)
set("CMAKE_COLOR_DIAGNOSTICS" "ON" CACHE "BOOL" "" FORCE)
set("QT_MAINTENANCE_TOOL" "E:/YingYong/QT/MaintenanceTool.exe" CACHE "FILEPATH" "" FORCE)
set("CMAKE_BUILD_TYPE" "Debug" CACHE "STRING" "" FORCE)
set("CMAKE_CXX_COMPILER" "E:/YingYong/VisualStudio/Community/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/cl.exe" CACHE "FILEPATH" "" FORCE)