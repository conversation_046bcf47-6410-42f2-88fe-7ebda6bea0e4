# Generated by CMake. Changes will be overwritten.
D:/Desktop_xiao/100/Client/mainwindow.h
 mmc:Q_OBJECT
 mdp:D:/Desktop_xiao/100/Client/mainwindow.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/q17memory.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/q20functional.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/q20iterator.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/q20memory.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/q20type_traits.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/q20utility.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/q23utility.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qalgorithms.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qanystringview.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qarraydata.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qarraydataops.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qarraydatapointer.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qassert.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qatomic.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qatomic_cxx11.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qbasicatomic.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qbindingstorage.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qbytearray.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qbytearrayalgorithms.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qbytearraylist.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qbytearrayview.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qchar.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qcompare.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qcompare_impl.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qcomparehelpers.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qcompilerdetection.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qconfig.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qconstructormacros.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qcontainerfwd.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qcontainerinfo.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qcontainertools_impl.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qcontiguouscache.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qdarwinhelpers.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qdatastream.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qdebug.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qendian.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qexceptionhandling.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qflags.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qfloat16.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qforeach.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qfunctionaltools_impl.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qfunctionpointer.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qgenericatomic.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qglobal.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qglobalstatic.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qhash.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qhashfunctions.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qiodevicebase.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qiterable.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qiterator.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qlatin1stringview.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qline.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qlist.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qlogging.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qmalloc.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qmap.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qmargins.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qmath.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qmetacontainer.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qmetatype.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qminmax.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qnamespace.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qnumeric.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qobject.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qobject_impl.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qobjectdefs.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qobjectdefs_impl.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qoverload.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qpair.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qpoint.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qprocessordetection.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qrect.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qrefcount.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qscopedpointer.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qscopeguard.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qset.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qshareddata.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qshareddata_impl.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qsharedpointer.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qsharedpointer_impl.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qsize.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qspan.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qstdlibdetection.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qstring.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qstringalgorithms.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qstringbuilder.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qstringconverter.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qstringconverter_base.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qstringfwd.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qstringlist.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qstringliteral.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qstringmatcher.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qstringtokenizer.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qstringview.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qswap.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qsysinfo.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qsystemdetection.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qtaggedpointer.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qtclasshelpermacros.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qtconfiginclude.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qtconfigmacros.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qtcore-config.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qtcoreexports.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qtcoreglobal.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qtdeprecationmarkers.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qtenvironmentvariables.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qtextstream.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qtformat_impl.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qtmetamacros.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qtnoop.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qtpreprocessorsupport.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qtresource.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qttranslation.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qttypetraits.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qtversion.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qtversionchecks.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qtypeinfo.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qtypes.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qutf8stringview.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qvariant.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qvarlengtharray.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qversiontagging.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qxptype_traits.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore/qyieldcpu.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtGui/qaction.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtGui/qbitmap.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtGui/qbrush.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtGui/qcolor.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtGui/qcursor.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtGui/qfont.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtGui/qfontinfo.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtGui/qfontmetrics.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtGui/qfontvariableaxis.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtGui/qicon.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtGui/qimage.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtGui/qkeysequence.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtGui/qpaintdevice.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtGui/qpalette.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtGui/qpixelformat.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtGui/qpixmap.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtGui/qpolygon.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtGui/qregion.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtGui/qrgb.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtGui/qrgba64.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtGui/qtgui-config.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtGui/qtguiexports.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtGui/qtguiglobal.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtGui/qtransform.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtGui/qwindowdefs.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtGui/qwindowdefs_win.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtWidgets/QMainWindow
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtWidgets/qmainwindow.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtWidgets/qsizepolicy.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtWidgets/qtabwidget.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtWidgets/qtwidgets-config.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtWidgets/qtwidgetsexports.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtWidgets/qtwidgetsglobal.h
 mdp:E:/YingYong/QT/6.9.1/msvc2022_64/include/QtWidgets/qwidget.h
D:/Desktop_xiao/100/Client/main.cpp
D:/Desktop_xiao/100/Client/mainwindow.cpp
 uic:./ui_mainwindow.h
