{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "E:/YingYong/QT/Tools/CMake_64/bin/cmake.exe", "cpack": "E:/YingYong/QT/Tools/CMake_64/bin/cpack.exe", "ctest": "E:/YingYong/QT/Tools/CMake_64/bin/ctest.exe", "root": "E:/YingYong/QT/Tools/CMake_64/share/cmake-3.30"}, "version": {"isDirty": false, "major": 3, "minor": 30, "patch": 5, "string": "3.30.5", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-dfacab46e714b486da38.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "cache-v2-d67b9a133b8016a34ded.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-cf516a4c47d8bc5eca62.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}], "reply": {"cache-v2": {"jsonFile": "cache-v2-d67b9a133b8016a34ded.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-cf516a4c47d8bc5eca62.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, "codemodel-v2": {"jsonFile": "codemodel-v2-dfacab46e714b486da38.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}}}