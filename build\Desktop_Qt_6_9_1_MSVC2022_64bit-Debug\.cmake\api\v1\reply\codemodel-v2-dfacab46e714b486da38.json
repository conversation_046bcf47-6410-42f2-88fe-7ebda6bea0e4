{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-Debug-702ef5d46ab20068e0da.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "Client", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "Client::@6890427a1f51a3e7e1df", "jsonFile": "target-Client-Debug-8766139fc1073c3c26da.json", "name": "Client", "projectIndex": 0}, {"directoryIndex": 0, "id": "Client_autogen::@6890427a1f51a3e7e1df", "jsonFile": "target-Client_autogen-Debug-cbbddb531f6f2a65b385.json", "name": "Client_autogen", "projectIndex": 0}, {"directoryIndex": 0, "id": "Client_autogen_timestamp_deps::@6890427a1f51a3e7e1df", "jsonFile": "target-Client_autogen_timestamp_deps-Debug-d917aace47016758ebb7.json", "name": "Client_autogen_timestamp_deps", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/Desktop_xiao/100/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "source": "D:/Desktop_xiao/100/Client"}, "version": {"major": 2, "minor": 7}}